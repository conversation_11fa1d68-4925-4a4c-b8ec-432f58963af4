import Mathlib.Data.Nat.Basic
import Mathlib.Algebra.Order.Ring.Basic
import Mathlib.Data.Finite.Defs
import Mathlib.Data.Nat.Set
import Mathlib.Data.Set.Finite.Basic

open Nat

-- AMC 12A 2002 Problem 6: Determine how many positive integers m admit
-- at least one positive integer n such that mn ≤ m + n

-- Main theorem: All positive integers m work
theorem amc12a_2002_p6 :
  ∀ m : ℕ, m > 0 → ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n := by
  intro m hm
  -- Use n = 1 as the witness
  use 1
  constructor
  · -- Show 1 > 0
    exact Nat.one_pos
  · -- Show m * 1 ≤ m + 1
    rw [mul_one]
    exact Nat.le_add_right m 1

-- Corollary: There are infinitely many such positive integers m
theorem infinitely_many_m :
  Set.Infinite {m : ℕ | m > 0 ∧ ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n} := by
  -- Since all positive integers work, the set equals ℕ+
  have h_eq : {m : ℕ | m > 0 ∧ ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n} = {m : ℕ | m > 0} := by
    ext m
    constructor
    · intro h
      exact h.1
    · intro hm
      constructor
      · exact hm
      · exact amc12a_2002_p6 m hm
  rw [h_eq]
  -- Show {m : ℕ | m > 0} = range Nat.succ and use infinite range
  have h_eq_range : {m : ℕ | m > 0} = Set.range Nat.succ := by
    rw [← Nat.range_succ]
  rw [h_eq_range]
  exact Set.infinite_range_of_injective Nat.succ_injective

-- Alternative proof using algebraic manipulation
theorem amc12a_2002_p6_alt :
  ∀ m : ℕ, m > 0 → ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n := by
  intro m hm
  -- Rewrite mn ≤ m + n as (m-1)(n-1) ≤ 1
  have h_equiv : ∀ (a b : ℕ), a > 0 → b > 0 →
    (a * b ≤ a + b ↔ (a - 1) * (b - 1) ≤ 1) := by
    sorry -- SUBGOAL_003: Prove the equivalence (complex algebraic manipulation)

  use 1
  constructor
  · -- Show 1 > 0
    exact Nat.one_pos
  · -- Use the equivalence with n = 1
    rw [h_equiv m 1 hm Nat.one_pos]
    -- Show (m-1) * (1-1) = (m-1) * 0 = 0 ≤ 1
    simp [Nat.mul_zero]
