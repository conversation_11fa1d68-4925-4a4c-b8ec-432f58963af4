import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith

-- Theorem: Solve the linear system f + 3z = 11, 3(f - 1) - 5z = -68
theorem linear_system_solution :
  ∃! p : ℝ × ℝ, p.1 + 3 * p.2 = 11 ∧ 3 * (p.1 - 1) - 5 * p.2 = -68 ∧ p.1 = -10 ∧ p.2 = 7 := by
  -- Step 1: Rewrite the second equation to standard form
  have h1 : ∀ f z : ℝ, 3 * (f - 1) - 5 * z = -68 ↔ 3 * f - 5 * z = -65 := by
    intro f z
    constructor
    · intro h
      calc 3 * f - 5 * z
        = 3 * f - 3 - 5 * z + 3 := by ring
        _ = 3 * (f - 1) - 5 * z + 3 := by ring
        _ = -68 + 3 := by rw [h]
        _ = -65 := by norm_num
    · intro h
      calc 3 * (f - 1) - 5 * z
        = 3 * f - 3 - 5 * z := by ring
        _ = 3 * f - 5 * z - 3 := by ring
        _ = -65 - 3 := by rw [h]
        _ = -68 := by norm_num

  -- Step 2: Solve the system using elimination method
  have h2 : ∀ f z : ℝ, f + 3 * z = 11 ∧ 3 * f - 5 * z = -65 → z = 7 := by
    intro f z ⟨eq1, eq2⟩
    -- Multiply eq1 by 3: 3f + 9z = 33
    have eq1_mult : 3 * f + 9 * z = 33 := by linarith [eq1]
    -- Subtract eq2 from eq1_mult: (3f + 9z) - (3f - 5z) = 33 - (-65)
    have elim : 14 * z = 98 := by linarith [eq1_mult, eq2]
    -- Solve for z: z = 98/14 = 7
    linarith [elim]

  -- Step 3: Find f by substitution
  have h3 : ∀ f : ℝ, f + 3 * 7 = 11 → f = -10 := by
    intro f h
    -- f + 21 = 11, so f = 11 - 21 = -10
    linarith [h]

  -- Step 4: Verify the solution satisfies both equations
  have h4 : (-10) + 3 * 7 = 11 ∧ 3 * ((-10) - 1) - 5 * 7 = -68 := by
    constructor
    · norm_num  -- (-10) + 3 * 7 = -10 + 21 = 11
    · norm_num  -- 3 * (-11) - 5 * 7 = -33 - 35 = -68

  -- Main proof combining all steps
  use (-10, 7)
  constructor
  · -- Prove existence: the pair (-10, 7) satisfies all conditions
    constructor
    · -- First equation: (-10) + 3 * 7 = 11
      norm_num
    constructor
    · -- Second equation: 3 * ((-10) - 1) - 5 * 7 = -68
      norm_num
    constructor
    · -- f = -10
      rfl
    · -- z = 7
      rfl
  · -- Prove uniqueness: if any pair (f, z) satisfies the conditions, then f = -10 and z = 7
    intro ⟨f, z⟩ ⟨eq1, eq2, hf, hz⟩
    -- From the equations and our proven lemmas
    have std_eq2 : 3 * f - 5 * z = -65 := (h1 f z).mp eq2
    have z_eq : z = 7 := h2 f z ⟨eq1, std_eq2⟩
    have f_eq : f = -10 := h3 f (by rw [z_eq] at eq1; exact eq1)
    -- Conclude the pair equals (-10, 7)
    ext
    · exact f_eq
    · exact z_eq
